/**
 * Firestore Persistence Utility
 * Manages user-scoped roadmap data storage with Firebase Firestore
 */

import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  writeBatch,
  onSnapshot,
  increment,
} from "firebase/firestore";
import { db } from "../config/firebase";

class FirestorePersistence {
  /**
   * Generate a unique ID for a roadmap
   */
  static generateRoadmapId(title, userId) {
    const timestamp = Date.now();
    const cleanTitle = title.replace(/[^a-zA-Z0-9]/g, "-").toLowerCase();
    const userPrefix = userId.substring(0, 8);
    return `${userPrefix}-${cleanTitle}-${timestamp}`;
  }

  /**
   * Save a roadmap with user ownership
   */
  static async saveRoadmap(roadmapData, userId, originalData = null) {
    if (!userId) {
      throw new Error("User must be authenticated to save roadmaps");
    }

    try {
      const roadmapId = this.generateRoadmapId(roadmapData.title, userId);
      const timestamp = serverTimestamp();

      // Prepare roadmap document
      const roadmapDoc = {
        id: roadmapId,
        userId: userId,
        data: roadmapData,
        originalData: originalData || roadmapData,
        isPublic: false, // Default to private
        createdAt: timestamp,
        updatedAt: timestamp,
        lastAccessed: timestamp,
        version: 1,
        tags: roadmapData.tags || [],
        projectLevel: roadmapData.project_level || "beginner",
      };

      // Prepare metadata document for efficient querying
      const metadataDoc = {
        id: roadmapId,
        userId: userId,
        title: roadmapData.title,
        description: roadmapData.description || "",
        projectLevel: roadmapData.project_level || "beginner",
        tags: roadmapData.tags || [],
        isPublic: false,
        createdAt: timestamp,
        updatedAt: timestamp,
        lastAccessed: timestamp,
        totalPhases: this.calculateTotalPhases(roadmapData),
        totalTasks: this.calculateTotalTasks(roadmapData),
        progressPercentage: 0,
        viewCount: 0,
        likeCount: 0,
      };

      // Use batch write for consistency
      const batch = writeBatch(db);

      // Save roadmap document
      const roadmapRef = doc(db, "roadmaps", roadmapId);
      batch.set(roadmapRef, roadmapDoc);

      // Save metadata document
      const metadataRef = doc(db, "roadmapMetadata", roadmapId);
      batch.set(metadataRef, metadataDoc);

      await batch.commit();

      console.log("✅ Roadmap saved to Firestore:", roadmapId);
      return roadmapId;
    } catch (error) {
      console.error("❌ Error saving roadmap to Firestore:", error);
      throw new Error("Failed to save roadmap: " + error.message);
    }
  }

  /**
   * Load a specific roadmap
   */
  static async loadRoadmap(roadmapId, userId = null) {
    try {
      const roadmapRef = doc(db, "roadmaps", roadmapId);
      const roadmapSnap = await getDoc(roadmapRef);

      if (!roadmapSnap.exists()) {
        return null;
      }

      const roadmapData = roadmapSnap.data();

      // Check access permissions
      if (!roadmapData.isPublic && (!userId || roadmapData.userId !== userId)) {
        throw new Error("Access denied: This roadmap is private");
      }

      // Update last accessed time if user owns the roadmap
      if (userId && roadmapData.userId === userId) {
        await this.updateLastAccessed(roadmapId);
      }

      return {
        id: roadmapData.id,
        data: roadmapData.data,
        originalData: roadmapData.originalData,
        isPublic: roadmapData.isPublic,
        userId: roadmapData.userId,
        createdAt: roadmapData.createdAt,
        updatedAt: roadmapData.updatedAt,
        lastAccessed: roadmapData.lastAccessed,
      };
    } catch (error) {
      console.error("❌ Error loading roadmap from Firestore:", error);
      throw error;
    }
  }

  /**
   * Get all roadmaps for a specific user
   */
  static async getUserRoadmaps(userId) {
    if (!userId) {
      throw new Error("User ID is required");
    }

    try {
      const metadataRef = collection(db, "roadmapMetadata");
      const q = query(
        metadataRef,
        where("userId", "==", userId),
        orderBy("lastAccessed", "desc")
      );

      const querySnapshot = await getDocs(q);
      const roadmaps = [];

      querySnapshot.forEach((doc) => {
        roadmaps.push({
          id: doc.id,
          ...doc.data(),
        });
      });

      return roadmaps;
    } catch (error) {
      console.error("❌ Error getting user roadmaps:", error);
      throw new Error("Failed to load user roadmaps: " + error.message);
    }
  }

  /**
   * Get public roadmaps for community section
   */
  static async getPublicRoadmaps(limitCount = 20) {
    try {
      const metadataRef = collection(db, "roadmapMetadata");

      // Try optimized query first, fall back to simple query if index not ready
      let q;
      try {
        q = query(
          metadataRef,
          where("isPublic", "==", true),
          orderBy("updatedAt", "desc"),
          limit(limitCount)
        );
      } catch (indexError) {
        console.log("📊 Using fallback query (index not ready)");
        q = query(
          metadataRef,
          where("isPublic", "==", true),
          limit(limitCount)
        );
      }

      const querySnapshot = await getDocs(q);
      const roadmaps = [];

      querySnapshot.forEach((doc) => {
        roadmaps.push({
          id: doc.id,
          ...doc.data(),
        });
      });

      // Sort in memory if we used the fallback query
      if (!q._query.orderBy || q._query.orderBy.length === 0) {
        roadmaps.sort((a, b) => {
          const aTime = a.updatedAt?.toDate?.() || new Date(a.updatedAt || 0);
          const bTime = b.updatedAt?.toDate?.() || new Date(b.updatedAt || 0);
          return bTime - aTime;
        });
      }

      return roadmaps;
    } catch (error) {
      console.error("❌ Error getting public roadmaps:", error);
      throw new Error("Failed to load public roadmaps: " + error.message);
    }
  }

  /**
   * Update roadmap privacy setting
   */
  static async updateRoadmapPrivacy(roadmapId, isPublic, userId) {
    if (!userId) {
      throw new Error("User must be authenticated");
    }

    try {
      const batch = writeBatch(db);

      // Update roadmap document
      const roadmapRef = doc(db, "roadmaps", roadmapId);
      batch.update(roadmapRef, {
        isPublic: isPublic,
        updatedAt: serverTimestamp(),
      });

      // Update metadata document
      const metadataRef = doc(db, "roadmapMetadata", roadmapId);
      batch.update(metadataRef, {
        isPublic: isPublic,
        updatedAt: serverTimestamp(),
      });

      await batch.commit();

      console.log(
        `✅ Roadmap privacy updated: ${roadmapId} -> ${
          isPublic ? "public" : "private"
        }`
      );
      return true;
    } catch (error) {
      console.error("❌ Error updating roadmap privacy:", error);
      throw new Error("Failed to update roadmap privacy: " + error.message);
    }
  }

  /**
   * Update roadmap data
   */
  static async updateRoadmap(roadmapId, roadmapData, userId) {
    if (!userId) {
      throw new Error("User must be authenticated");
    }

    try {
      const batch = writeBatch(db);

      // Update roadmap document
      const roadmapRef = doc(db, "roadmaps", roadmapId);
      batch.update(roadmapRef, {
        data: roadmapData,
        updatedAt: serverTimestamp(),
        lastAccessed: serverTimestamp(),
        version: increment(1),
      });

      // Update metadata document
      const metadataRef = doc(db, "roadmapMetadata", roadmapId);
      batch.update(metadataRef, {
        title: roadmapData.title,
        description: roadmapData.description || "",
        projectLevel: roadmapData.project_level || "beginner",
        tags: roadmapData.tags || [],
        updatedAt: serverTimestamp(),
        lastAccessed: serverTimestamp(),
        totalPhases: this.calculateTotalPhases(roadmapData),
        totalTasks: this.calculateTotalTasks(roadmapData),
      });

      await batch.commit();

      console.log("✅ Roadmap updated in Firestore:", roadmapId);
      return true;
    } catch (error) {
      console.error("❌ Error updating roadmap:", error);
      throw new Error("Failed to update roadmap: " + error.message);
    }
  }

  /**
   * Delete a roadmap
   */
  static async deleteRoadmap(roadmapId, userId) {
    if (!userId) {
      throw new Error("User must be authenticated");
    }

    try {
      const batch = writeBatch(db);

      // Delete roadmap document
      const roadmapRef = doc(db, "roadmaps", roadmapId);
      batch.delete(roadmapRef);

      // Delete metadata document
      const metadataRef = doc(db, "roadmapMetadata", roadmapId);
      batch.delete(metadataRef);

      // Delete task completions
      const completionRef = doc(
        db,
        "taskCompletions",
        userId,
        "roadmaps",
        roadmapId
      );
      batch.delete(completionRef);

      await batch.commit();

      console.log("✅ Roadmap deleted from Firestore:", roadmapId);
      return true;
    } catch (error) {
      console.error("❌ Error deleting roadmap:", error);
      throw new Error("Failed to delete roadmap: " + error.message);
    }
  }

  /**
   * Update last accessed time
   */
  static async updateLastAccessed(roadmapId) {
    try {
      const metadataRef = doc(db, "roadmapMetadata", roadmapId);
      await updateDoc(metadataRef, {
        lastAccessed: serverTimestamp(),
      });
    } catch (error) {
      console.error("❌ Error updating last accessed:", error);
      // Don't throw error for this non-critical operation
    }
  }

  /**
   * Calculate total phases in roadmap
   */
  static calculateTotalPhases(roadmapData) {
    return roadmapData.phases ? roadmapData.phases.length : 0;
  }

  /**
   * Calculate total tasks in roadmap
   */
  static calculateTotalTasks(roadmapData) {
    if (!roadmapData.phases) return 0;

    return roadmapData.phases.reduce((total, phase) => {
      return total + (phase.tasks ? phase.tasks.length : 0);
    }, 0);
  }

  /**
   * Subscribe to user's roadmaps (real-time updates)
   */
  static subscribeToUserRoadmaps(userId, callback) {
    if (!userId) {
      throw new Error("User ID is required");
    }

    const metadataRef = collection(db, "roadmapMetadata");
    const q = query(
      metadataRef,
      where("userId", "==", userId),
      orderBy("lastAccessed", "desc")
    );

    return onSnapshot(
      q,
      (querySnapshot) => {
        const roadmaps = [];
        querySnapshot.forEach((doc) => {
          roadmaps.push({
            id: doc.id,
            ...doc.data(),
          });
        });
        callback(roadmaps);
      },
      (error) => {
        console.error("❌ Error in roadmaps subscription:", error);
        callback([]);
      }
    );
  }

  /**
   * Subscribe to public roadmaps (real-time updates)
   */
  static subscribeToPublicRoadmaps(callback, limitCount = 20) {
    const metadataRef = collection(db, "roadmapMetadata");

    // Try optimized query first, fall back to simple query if index not ready
    let q;
    try {
      q = query(
        metadataRef,
        where("isPublic", "==", true),
        orderBy("updatedAt", "desc"),
        limit(limitCount)
      );
    } catch (indexError) {
      console.log("📊 Using fallback subscription query (index not ready)");
      q = query(metadataRef, where("isPublic", "==", true), limit(limitCount));
    }

    return onSnapshot(
      q,
      (querySnapshot) => {
        const roadmaps = [];
        querySnapshot.forEach((doc) => {
          roadmaps.push({
            id: doc.id,
            ...doc.data(),
          });
        });

        // Sort in memory if we used the fallback query
        if (!q._query.orderBy || q._query.orderBy.length === 0) {
          roadmaps.sort((a, b) => {
            const aTime = a.updatedAt?.toDate?.() || new Date(a.updatedAt || 0);
            const bTime = b.updatedAt?.toDate?.() || new Date(b.updatedAt || 0);
            return bTime - aTime;
          });
        }

        callback(roadmaps);
      },
      (error) => {
        console.error("❌ Error in public roadmaps subscription:", error);
        callback([]);
      }
    );
  }
}

export default FirestorePersistence;
